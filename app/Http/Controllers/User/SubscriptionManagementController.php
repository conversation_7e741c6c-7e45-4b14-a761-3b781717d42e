<?php

namespace App\Http\Controllers\User;

use App\Models\Feature;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;
use App\Services\SubscriptionEMandateService;

class SubscriptionManagementController extends Controller
{
    /**
     * Display a listing of the user's subscriptions.
     */
    public function index()
    {
        $user = auth()->user();

        if ($user->isBizappUser === 'Y' && !$user->companies) {
            return redirect()->back()->with('error', 'Please renew on Bizapp platform.');
        }

        // Get current active subscription
        $activeSubscription = Subscription::where('company_id', $user->companies->id)
            ->where('status', 'active')
            ->whereNull('canceled_at')
            ->where(function($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->with('subscriptionPlan')
            ->first();

        // Get subscription history
        $subscriptionHistory = Subscription::where('company_id', $user->companies->id)
            ->where(function($query) use ($activeSubscription) {
                if ($activeSubscription) {
                    $query->where('id', '!=', $activeSubscription->id);
                }
            })
            ->with('subscriptionPlan')
            ->orderBy('created_at', 'desc')
            ->get();
        // Get all available plans bar legacy plan
        $plans = SubscriptionPlan::where('is_active', true)
            ->where('name', '!=', 'Legacy Unlimited')
            ->orderBy('price')
            ->get();

        // Get all active features for comparison, only including those with codes starting with AO- or CF-
        $allFeatures = Feature::where('is_active', true)
            ->where(function($query) {
                $query->where('code', 'like', 'AO-%')
                      ->orWhere('code', 'like', 'CF-%');
            })
            ->orderBy('name')
            ->get();

        return view('user.subscriptions.index', compact(
            'activeSubscription',
            'subscriptionHistory',
            'plans',
            'allFeatures'
        ));
    }

    /**
     * Display the details of a specific subscription.
     */
    public function show($id)
    {
        $user = auth()->user();

        $subscription = Subscription::where('id', $id)
            ->where('company_id', $user->companies->id)
            ->with('subscriptionPlan.features')
            ->firstOrFail();

        // Get the plan from the subscription to match the view's expectations
        $plan = $subscription->subscriptionPlan;

        return view('user.subscriptions.show', compact('subscription', 'plan'));
    }

    /**
     * Display the details of a specific plan.
     */
    public function showPlan($id)
    {
        $user = auth()->user();

        // Get the plan details
        $plan = SubscriptionPlan::with('features')->findOrFail($id);

        return view('user.subscriptions.show', compact('plan'));
    }

    /**
     * Show the form for upgrading to a new subscription plan.
     */
    public function upgradeForm($planId)
    {
        $user = auth()->user();

        if (!$user->companies) {
            return redirect()->route('company.wizard')
                ->with('error', 'Please complete your company profile first.');
        }

        // Get the plan to upgrade to
        $plan = SubscriptionPlan::findOrFail($planId);

        // Get current active subscription
        $currentSubscription = Subscription::where('company_id', $user->companies->id)
            ->where('status', 'active')
            ->whereNull('canceled_at')
            ->where(function($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->with('subscriptionPlan')
            ->first();

        if (!$currentSubscription) {
            return redirect()->route('user.subscriptions.index')
                ->with('error', 'You do not have an active subscription to upgrade.');
        }

        // Check if the new plan is actually an upgrade
        if ($plan->price <= $currentSubscription->subscriptionPlan->price) {
            return redirect()->route('user.subscriptions.index')
                ->with('error', 'The selected plan is not an upgrade from your current plan.');
        }

        return view('user.subscriptions.upgrade', compact('plan', 'currentSubscription'));
    }

    /**
     * Process the subscription upgrade using BayarCash E-Mandate.
     */
    public function processUpgrade(Request $request, $planId, SubscriptionEMandateService $emandateService)
    {
        $user = auth()->user();

        if (!$user->companies) {
            return redirect()->route('company.wizard')
                ->with('error', 'Please complete your company profile first.');
        }

        // Get the plan to upgrade to
        $plan = SubscriptionPlan::findOrFail($planId);

        // Get current active subscription
        $currentSubscription = Subscription::where('company_id', $user->companies->id)
            ->where('status', 'active')
            ->whereNull('canceled_at')
            ->where(function($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->first();

        if (!$currentSubscription) {
            return redirect()->route('user.subscriptions.index')
                ->with('error', 'You do not have an active subscription to upgrade.');
        }

        // Check if the new plan is actually an upgrade
        if ($plan->price <= $currentSubscription->subscriptionPlan->price) {
            return redirect()->route('user.subscriptions.index')
                ->with('error', 'The selected plan is not an upgrade from your current plan.');
        }

        DB::beginTransaction();
        try {
            // Calculate upgrade amount (pro-rated if needed)
            $upgradeAmount = $this->calculateUpgradeAmount($currentSubscription, $plan);

            // Cancel the current subscription (but let it run until the end of its term)
            $currentSubscription->update([
                'cancels_at' => $currentSubscription->ends_at,
                'canceled_at' => now()
            ]);

            // Calculate subscription dates for the new subscription
            $startsAt = now();
            $endsAt = null;

            if ($plan->duration_in_seconds > 0) {
                $endsAt = $startsAt->copy()->addSeconds($plan->duration_in_seconds);
            }

            // Calculate trial period if applicable
            $trialEndsAt = null;
            if ($plan->trial_period_days > 0) {
                $trialEndsAt = $startsAt->copy()->addDays($plan->trial_period_days);
                // If there's a trial, the subscription starts after the trial
                $endsAt = $trialEndsAt->copy()->addSeconds($plan->duration_in_seconds);
            }

            // Create new subscription with pending status (will be activated after e-mandate setup)
            $newSubscription = Subscription::create([
                'company_id' => $user->companies->id,
                'subscription_plan_id' => $plan->id,
                'starts_at' => $startsAt,
                'ends_at' => $endsAt,
                'trial_ends_at' => $trialEndsAt,
                'status' => 'pending', // Will be activated after e-mandate enrollment
                'upgrade_amount' => $upgradeAmount,
                'is_upgrade' => true,
                'previous_subscription_id' => $currentSubscription->id
            ]);

            DB::commit();

            // Process BayarCash E-Mandate enrollment for subscription upgrade
            Log::info('Processing subscription upgrade with BayarCash E-Mandate', [
                'subscription_id' => $newSubscription->id,
                'plan_id' => $plan->id,
                'upgrade_amount' => $upgradeAmount,
                'user_id' => $user->id,
                'company_id' => $user->companies->id
            ]);

            // Create e-mandate enrollment for the upgrade
            $emandateResult = $emandateService->createSubscriptionEMandate($newSubscription);

            if ($emandateResult['success']) {
                if ($emandateResult['is_existing']) {
                    // Existing e-mandate found and linked
                    Log::info('Subscription upgrade linked to existing e-mandate', [
                        'subscription_id' => $newSubscription->id,
                        'enrollment_id' => $emandateResult['enrollment']->id
                    ]);

                    // Activate the subscription immediately since e-mandate already exists
                    $newSubscription->update(['status' => 'active']);

                    return redirect()->route('user.subscriptions.thankyou', $newSubscription->id)
                        ->with('success', 'Your subscription has been upgraded successfully using your existing e-mandate enrollment.');
                } else {
                    // New e-mandate enrollment created, redirect to BayarCash
                    Log::info('Redirecting to BayarCash e-mandate enrollment for subscription upgrade', [
                        'subscription_id' => $newSubscription->id,
                        'enrollment_url' => $emandateResult['redirect_url']
                    ]);

                    return redirect($emandateResult['redirect_url']);
                }
            } else {
                // E-mandate enrollment failed
                Log::error('Failed to create e-mandate enrollment for subscription upgrade', [
                    'subscription_id' => $newSubscription->id,
                    'error' => $emandateResult['message']
                ]);

                // Rollback the subscription creation
                $newSubscription->delete();
                $currentSubscription->update([
                    'cancels_at' => null,
                    'canceled_at' => null
                ]);

                return redirect()->route('user.subscriptions.management')
                    ->with('error', 'Failed to set up e-mandate enrollment for your subscription upgrade: ' . $emandateResult['message']);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Subscription upgrade failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'plan_id' => $planId
            ]);

            return redirect()->route('user.subscriptions.index')
                ->with('error', 'Failed to upgrade subscription. Please try again later.');
        }
    }

    /**
     * Show the cancellation confirmation page.
     */
    public function cancelForm()
    {
        $user = auth()->user();

        $subscription = Subscription::where('company_id', $user->companies->id)
            ->where('status', 'active')
            ->whereNull('canceled_at')
            ->with('subscriptionPlan')
            ->firstOrFail();

        return view('user.subscriptions.cancel', compact('subscription'));
    }

    /**
     * Process the subscription cancellation.
     */
    public function processCancel(Request $request)
    {
        $user = auth()->user();

        $subscription = Subscription::where('company_id', $user->companies->id)
            ->where('status', 'active')
            ->whereNull('canceled_at')
            ->firstOrFail();

        $cancelImmediately = $request->has('cancel_immediately') && $request->cancel_immediately === 'yes';

        if ($cancelImmediately) {
            // Cancel immediately
            $subscription->update([
                'ends_at' => now(),
                'cancels_at' => now(),
                'canceled_at' => now(),
                'status' => 'canceled'
            ]);

            return redirect()->route('user.subscriptions.management')
                ->with('success', 'Your subscription has been canceled immediately.');
        } else {
            // Cancel at the end of the billing period
            $subscription->update([
                'cancels_at' => $subscription->ends_at,
                'canceled_at' => now()
            ]);

            return redirect()->route('user.subscriptions.management')
                ->with('success', 'Your subscription will be canceled at the end of the current billing period.');
        }
    }

    /**
     * Resume a canceled subscription (if it hasn't ended yet).
     */
    public function resume($id)
    {
        $user = auth()->user();

        $subscription = Subscription::where('id', $id)
            ->where('company_id', $user->companies->id)
            ->whereNotNull('canceled_at')
            ->where('status', '!=', 'canceled') // Not already fully canceled
            ->where(function($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->firstOrFail();

        $subscription->update([
            'cancels_at' => null,
            'canceled_at' => null
        ]);

        return redirect()->route('user.subscriptions.management')
            ->with('success', 'Your subscription has been resumed.');
    }

    /**
     * Calculate the upgrade amount with pro-rating logic
     *
     * @param Subscription $currentSubscription
     * @param SubscriptionPlan $newPlan
     * @return float
     */
    private function calculateUpgradeAmount(Subscription $currentSubscription, SubscriptionPlan $newPlan): float
    {
        $currentPlan = $currentSubscription->subscriptionPlan;

        // Basic upgrade amount (difference between plans)
        $upgradeAmount = $newPlan->price - $currentPlan->price;

        // If current subscription has an end date, calculate pro-rated amount
        if ($currentSubscription->ends_at) {
            $totalDays = $currentSubscription->starts_at->diffInDays($currentSubscription->ends_at);
            $remainingDays = now()->diffInDays($currentSubscription->ends_at);

            if ($totalDays > 0 && $remainingDays > 0) {
                // Pro-rate the upgrade amount based on remaining subscription time
                $proRateMultiplier = $remainingDays / $totalDays;
                $upgradeAmount = $upgradeAmount * $proRateMultiplier;
            }
        }

        // Ensure minimum upgrade amount
        return max($upgradeAmount, 0);
    }
}
