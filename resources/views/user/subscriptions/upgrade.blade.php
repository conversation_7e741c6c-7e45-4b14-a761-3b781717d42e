@extends('backend.layout.default')

@section('content')
<div class="page-content">
    <section class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="alert-heading"><i class="bi bi-arrow-up-circle"></i> Subscription Upgrade</h4>
                    <p>You are about to upgrade from <strong>{{ $currentSubscription->subscriptionPlan->name }}</strong> to <strong>{{ $plan->name }}</strong>.</p>
                </div>
                <div class="card-body mt-4">

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-header bg-light mb-2">
                                    <h5 class="mb-0">Current Plan</h5>
                                </div>
                                <div class="card-body">
                                    <h4>{{ $currentSubscription->subscriptionPlan->name }}</h4>
                                    <div class="mb-3">
                                        <span class="badge bg-secondary">{{ ucfirst($currentSubscription->subscriptionPlan->tier) }}</span>
                                        <span class="badge bg-info">{{ ucfirst($currentSubscription->subscriptionPlan->billing_frequency) }}</span>
                                    </div>
                                    <h3 class="text-muted">RM{{ number_format($currentSubscription->subscriptionPlan->price, 2) }}</h3>
                                    <p>{{ $currentSubscription->subscriptionPlan->description }}</p>
                                </div>
                            </div>
                        </div>
                        {{-- <div class="col-md-2">
                            <div class="d-flex justify-content-center" style="height: 100%; padding-top: 100px;">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="bi bi-arrow-right text-white" style="font-size: 1.5rem;"></i>
                                </div>
                            </div>
                        </div> --}}
                        <div class="col-md-6">
                            <div class="card border border-primary">
                                <div class="card-header bg-primary mb-2">
                                    <h5 class="mb-0 text-white">New Plan</h5>
                                </div>
                                <div class="card-body">
                                    <h4>{{ $plan->name }}</h4>
                                    <div class="mb-3">
                                        <span class="badge bg-secondary">{{ ucfirst($plan->tier) }}</span>
                                        <span class="badge bg-info">{{ ucfirst($plan->billing_frequency) }}</span>
                                        @if($plan->trial_period_days > 0)
                                            <span class="badge bg-warning">{{ $plan->trial_period_days }} days trial</span>
                                        @endif
                                    </div>
                                    <h3 class="text-primary">RM{{ number_format($plan->price, 2) }}</h3>
                                    <p>{{ $plan->description }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Upgrade Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted">Price Difference</h6>
                                    <p>RM{{ number_format($plan->price - $currentSubscription->subscriptionPlan->price, 2) }} per {{ $plan->billing_frequency }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted">Current Subscription Status</h6>
                                    <p>
                                        @if($currentSubscription->canceled_at)
                                            <span class="badge bg-warning">Canceling</span>
                                            <small class="d-block mt-1">Will end on {{ $currentSubscription->ends_at->format('M d, Y') }}</small>
                                        @else
                                            <span class="badge bg-success">Active</span>
                                            <small class="d-block mt-1">Until {{ $currentSubscription->ends_at ? $currentSubscription->ends_at->format('M d, Y') : 'ongoing' }}</small>
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div class="alert alert-warning mt-3">
                                <p><strong>Note:</strong> Your current subscription will remain active until the end of its billing period. The new subscription will start immediately.</p>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="bi bi-credit-card"></i> Payment Method</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-bank"></i> FPX Direct Debit (E-Mandate)</h6>
                                <p class="mb-2">Your subscription upgrade will be processed using <strong>BayarCash E-Mandate</strong> (FPX Direct Debit). This provides:</p>
                                <ul class="mb-2">
                                    <li>Secure bank-to-bank transfers</li>
                                    <li>Automatic recurring payments</li>
                                    <li>No credit card required</li>
                                    <li>Direct debit from your bank account</li>
                                </ul>
                                <p class="mb-0"><small class="text-muted">You will be redirected to your bank's secure portal to authorize the e-mandate enrollment.</small></p>
                            </div>
                        </div>
                    </div>

                    <form action="{{ route('user.subscriptions.upgrade.process', $plan->id) }}" method="POST">
                        @csrf

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('user.subscriptions.management') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Go Back
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-arrow-up-circle"></i> Proceed to E-Mandate Setup
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection
